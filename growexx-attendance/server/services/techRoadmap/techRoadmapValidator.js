/**
 * Tech Roadmap Validator
 * Validation logic for tech roadmap related operations
 */

const Joi = require('joi');
const { customFunction } = require('../../util/validation');

// ==================== COURSE VALIDATION ====================

/**
 * Validation schema for adding a new course
 */
const addCourseSchema = Joi.object().keys({
    name: Joi.string().required().min(1).max(255),
    description: Joi.string().optional().max(1000),
    duration: Joi.string().optional().max(100),
    category: Joi.string().optional().max(100),
    level: Joi.string().valid('beginner', 'intermediate', 'advanced').optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    isActive: Joi.boolean().optional().default(true)
});

/**
 * Validate add course request
 */
const validateAddCourse = async (req, res, next) => {
    try {
        const { error } = addCourseSchema.validate(req.body);
        if (error) {
            return res.status(400).json(customFunction.sendResponse(0, error.details[0].message));
        }
        return next();
    } catch (err) {
        return res.status(500).json(customFunction.sendResponse(0, 'Internal server error'));
    }
};

// ==================== ASSIGNMENT VALIDATION ====================

/**
 * Validation schema for course assignment
 */
const assignCourseSchema = Joi.object().keys({
    menteeId: Joi.string().required().regex(/^[0-9a-fA-F]{24}$/),
    mentorId: Joi.string().required().regex(/^[0-9a-fA-F]{24}$/),
    courseId: Joi.string().optional().regex(/^[0-9a-fA-F]{24}$/),
    courseName: Joi.string().optional().min(1).max(255),
    targetMonth: Joi.date().optional(),
    description: Joi.string().optional().max(1000),
    learningMedium: Joi.string().optional().max(255),
    completionStatus: Joi.string().valid('pending', 'in-progress', 'completed').optional().default('pending'),
    completionPercentage: Joi.number().min(0).max(100).optional().default(0),
    approvalStatus: Joi.string().valid('pending', 'approved', 'rejected').optional().default('pending')
});

/**
 * Validate course assignment request
 */
const validateAssignCourse = async (req, res, next) => {
    try {
        const { error } = assignCourseSchema.validate(req.body);
        if (error) {
            return res.status(400).json(customFunction.sendResponse(0, error.details[0].message));
        }
        return next();
    } catch (err) {
        return res.status(500).json(customFunction.sendResponse(0, 'Internal server error'));
    }
};

/**
 * Validation schema for updating course status
 */
const updateCourseStatusSchema = Joi.object().keys({
    completionStatus: Joi.string().valid('pending', 'in-progress', 'completed').optional(),
    completionPercentage: Joi.number().min(0).max(100).optional(),
    approvalStatus: Joi.string().valid('pending', 'approved', 'rejected').optional(),
    rating: Joi.number().min(1).max(5).optional(),
    feedback: Joi.string().optional().max(1000)
});

/**
 * Validate update course status request
 */
const validateUpdateCourseStatus = async (req, res, next) => {
    try {
        const { error } = updateCourseStatusSchema.validate(req.body);
        if (error) {
            return res.status(400).json(customFunction.sendResponse(0, error.details[0].message));
        }
        return next();
    } catch (err) {
        return res.status(500).json(customFunction.sendResponse(0, 'Internal server error'));
    }
};

// ==================== DOCUMENT VALIDATION ====================

/**
 * Validation schema for document approval
 */
const documentApprovalSchema = Joi.object().keys({
    assignmentId: Joi.string().required().regex(/^[0-9a-fA-F]{24}$/),
    documentId: Joi.string().required().regex(/^[0-9a-fA-F]{24}$/),
    status: Joi.string().valid('approved', 'rejected').required(),
    comment: Joi.string().optional().max(500)
});

/**
 * Validate document approval request
 */
const validateDocumentApproval = async (req, res, next) => {
    try {
        const { error } = documentApprovalSchema.validate(req.body);
        if (error) {
            return res.status(400).json(customFunction.sendResponse(0, error.details[0].message));
        }
        return next();
    } catch (err) {
        return res.status(500).json(customFunction.sendResponse(0, 'Internal server error'));
    }
};

// ==================== COMMENT VALIDATION ====================

/**
 * Validation schema for updating comments
 */
const updateCommentsSchema = Joi.object().keys({
    assignmentId: Joi.string().required().regex(/^[0-9a-fA-F]{24}$/),
    commentType: Joi.string().valid('mentor', 'mentee').required(),
    comment: Joi.string().required().max(1000)
});

/**
 * Validate update comments request
 */
const validateUpdateComments = async (req, res, next) => {
    try {
        const { error } = updateCommentsSchema.validate(req.body);
        if (error) {
            return res.status(400).json(customFunction.sendResponse(0, error.details[0].message));
        }
        return next();
    } catch (err) {
        return res.status(500).json(customFunction.sendResponse(0, 'Internal server error'));
    }
};

// ==================== ASSIGNMENT UPDATE VALIDATION ====================

/**
 * Validation schema for updating assignment
 */
const updateAssignmentSchema = Joi.object().keys({
    completionPercentage: Joi.number().min(0).max(100).optional(),
    description: Joi.string().optional().max(1000),
    learningMedium: Joi.string().optional().max(255),
    targetMonth: Joi.date().optional()
});

/**
 * Validate update assignment request
 */
const validateUpdateAssignment = async (req, res, next) => {
    try {
        const { error } = updateAssignmentSchema.validate(req.body);
        if (error) {
            return res.status(400).json(customFunction.sendResponse(0, error.details[0].message));
        }
        return next();
    } catch (err) {
        return res.status(500).json(customFunction.sendResponse(0, 'Internal server error'));
    }
};

// ==================== PARAMETER VALIDATION ====================

/**
 * Validate MongoDB ObjectId parameter
 */
const validateObjectId = (paramName) => {
    return async (req, res, next) => {
        try {
            const id = req.params[paramName];
            if (!id || !/^[0-9a-fA-F]{24}$/.test(id)) {
                return res.status(400).json(customFunction.sendResponse(0, `Invalid ${paramName}`));
            }
            return next();
        } catch (err) {
            return res.status(500).json(customFunction.sendResponse(0, 'Internal server error'));
        }
    };
};

module.exports = {
    // Course validation
    validateAddCourse,
    // Assignment validation
    validateAssignCourse,
    validateUpdateCourseStatus,
    validateUpdateAssignment,
    // Document validation
    validateDocumentApproval,
    // Comment validation
    validateUpdateComments,
    // Parameter validation
    validateObjectId
};
