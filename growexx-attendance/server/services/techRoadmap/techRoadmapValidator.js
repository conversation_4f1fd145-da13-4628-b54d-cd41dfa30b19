/**
 * Tech Roadmap Validator
 * Validation logic for tech roadmap related operations
 */

const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

// ==================== COURSE VALIDATION ====================

/**
 * Class represents validations for course management
 */
class TechRoadmapValidator extends validation {
    constructor(body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * Validate add course request
     */
    validateAddCourse() {
        console.log('Validation body:', JSON.stringify(this.body, null, 2));
        const { name, description, duration, category, level } = this.body;

        console.log('Extracted name:', name);
        console.log('Name type:', typeof name);
        console.log('Name length:', name ? name.length : 'undefined');

        if (!name || name.trim() === '') {
            throw new GeneralError(this.__(this.REQUIRED, 'Course name'), 400);
        }

        if (name.length > 255) {
            throw new GeneralError(this.__(this.INVALID, 'Course name'), 400);
        }

        if (description && description.length > 1000) {
            throw new GeneralError(this.__(this.INVALID, 'Description'), 400);
        }

        if (duration && duration.length > 100) {
            throw new GeneralError(this.__(this.INVALID, 'Duration'), 400);
        }

        if (category && category.length > 100) {
            throw new GeneralError(this.__(this.INVALID, 'Category'), 400);
        }

        if (level && !['beginner', 'intermediate', 'advanced'].includes(level)) {
            throw new GeneralError(this.__(this.INVALID, 'Level'), 400);
        }
    }

    /**
     * Validate course assignment request
     */
    validateAssignCourse() {
        const { menteeId, mentorId, courseId, courseName, description, learningMedium } = this.body;

        if (!menteeId) {
            throw new GeneralError(this.__(this.REQUIRED, 'Mentee ID'), 400);
        }

        super.checkMongoId(menteeId, 'Mentee ID');

        if (!mentorId) {
            throw new GeneralError(this.__(this.REQUIRED, 'Mentor ID'), 400);
        }

        super.checkMongoId(mentorId, 'Mentor ID');

        if (courseId) {
            super.checkMongoId(courseId, 'Course ID');
        }

        if (courseName && courseName.length > 255) {
            throw new GeneralError(this.__(this.INVALID, 'Course name'), 400);
        }

        if (description && description.length > 1000) {
            throw new GeneralError(this.__(this.INVALID, 'Description'), 400);
        }

        if (learningMedium && learningMedium.length > 255) {
            throw new GeneralError(this.__(this.INVALID, 'Learning medium'), 400);
        }
    }

    /**
     * Validate update course status request
     */
    validateUpdateCourseStatus() {
        const { completionStatus, completionPercentage, approvalStatus, rating, feedback } = this.body;

        if (completionStatus && !['pending', 'in-progress', 'completed'].includes(completionStatus)) {
            throw new GeneralError(this.__(this.INVALID, 'Completion status'), 400);
        }

        if (completionPercentage !== undefined) {
            if (isNaN(completionPercentage) || completionPercentage < 0 || completionPercentage > 100) {
                throw new GeneralError(this.__(this.INVALID, 'Completion percentage'), 400);
            }
        }

        if (approvalStatus && !['pending', 'approved', 'rejected'].includes(approvalStatus)) {
            throw new GeneralError(this.__(this.INVALID, 'Approval status'), 400);
        }

        if (rating !== undefined) {
            if (isNaN(rating) || rating < 1 || rating > 5) {
                throw new GeneralError(this.__(this.INVALID, 'Rating'), 400);
            }
        }

        if (feedback && feedback.length > 1000) {
            throw new GeneralError(this.__(this.INVALID, 'Feedback'), 400);
        }
    }

    /**
     * Validate document approval request
     */
    validateDocumentApproval() {
        const { assignmentId, documentId, status, comment } = this.body;

        if (!assignmentId) {
            throw new GeneralError(this.__(this.REQUIRED, 'Assignment ID'), 400);
        }

        super.checkMongoId(assignmentId, 'Assignment ID');

        if (!documentId) {
            throw new GeneralError(this.__(this.REQUIRED, 'Document ID'), 400);
        }

        super.checkMongoId(documentId, 'Document ID');

        if (!status) {
            throw new GeneralError(this.__(this.REQUIRED, 'Status'), 400);
        }

        if (!['approved', 'rejected'].includes(status)) {
            throw new GeneralError(this.__(this.INVALID, 'Status'), 400);
        }

        if (comment && comment.length > 500) {
            throw new GeneralError(this.__(this.INVALID, 'Comment'), 400);
        }
    }

    /**
     * Validate update comments request
     */
    validateUpdateComments() {
        const { assignmentId, commentType, comment } = this.body;

        if (!assignmentId) {
            throw new GeneralError(this.__(this.REQUIRED, 'Assignment ID'), 400);
        }

        super.checkMongoId(assignmentId, 'Assignment ID');

        if (!commentType) {
            throw new GeneralError(this.__(this.REQUIRED, 'Comment type'), 400);
        }

        if (!['mentor', 'mentee'].includes(commentType)) {
            throw new GeneralError(this.__(this.INVALID, 'Comment type'), 400);
        }

        if (comment === undefined || comment === null) {
            throw new GeneralError(this.__(this.REQUIRED, 'Comment'), 400);
        }

        if (comment.length > 1000) {
            throw new GeneralError(this.__(this.INVALID, 'Comment'), 400);
        }
    }

    /**
     * Validate update assignment request
     */
    validateUpdateAssignment() {
        const { completionPercentage, description, learningMedium } = this.body;

        if (completionPercentage !== undefined) {
            if (isNaN(completionPercentage) || completionPercentage < 0 || completionPercentage > 100) {
                throw new GeneralError(this.__(this.INVALID, 'Completion percentage'), 400);
            }
        }

        if (description && description.length > 1000) {
            throw new GeneralError(this.__(this.INVALID, 'Description'), 400);
        }

        if (learningMedium && learningMedium.length > 255) {
            throw new GeneralError(this.__(this.INVALID, 'Learning medium'), 400);
        }
    }
}

// ==================== MIDDLEWARE FUNCTIONS ====================

/**
 * Middleware to validate add course request
 */
const validateAddCourse = async (req, res, next) => {
    try {
        const validator = new TechRoadmapValidator(req.body, res.__);
        validator.validateAddCourse();
        return next();
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message
        });
    }
};

/**
 * Middleware to validate course assignment request
 */
const validateAssignCourse = async (req, res, next) => {
    try {
        const validator = new TechRoadmapValidator(req.body, res.__);
        validator.validateAssignCourse();
        return next();
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message
        });
    }
};

/**
 * Middleware to validate update course status request
 */
const validateUpdateCourseStatus = async (req, res, next) => {
    try {
        const validator = new TechRoadmapValidator(req.body, res.__);
        validator.validateUpdateCourseStatus();
        return next();
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message
        });
    }
};

/**
 * Middleware to validate document approval request
 */
const validateDocumentApproval = async (req, res, next) => {
    try {
        const validator = new TechRoadmapValidator(req.body, res.__);
        validator.validateDocumentApproval();
        return next();
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message
        });
    }
};

/**
 * Middleware to validate update comments request
 */
const validateUpdateComments = async (req, res, next) => {
    try {
        const validator = new TechRoadmapValidator(req.body, res.__);
        validator.validateUpdateComments();
        return next();
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message
        });
    }
};

/**
 * Middleware to validate update assignment request
 */
const validateUpdateAssignment = async (req, res, next) => {
    try {
        const validator = new TechRoadmapValidator(req.body, res.__);
        validator.validateUpdateAssignment();
        return next();
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message
        });
    }
};

/**
 * Middleware to validate MongoDB ObjectId parameter
 */
const validateObjectId = (paramName) => {
    return async (req, res, next) => {
        try {
            const id = req.params[paramName];
            if (!id || !/^[0-9a-fA-F]{24}$/.test(id)) {
                return res.status(400).json({
                    status: 0,
                    message: `Invalid ${paramName}`
                });
            }
            return next();
        } catch (error) {
            return res.status(500).json({
                status: 0,
                message: 'Internal server error'
            });
        }
    };
};

module.exports = {
    // Course validation
    validateAddCourse,
    // Assignment validation
    validateAssignCourse,
    validateUpdateCourseStatus,
    validateUpdateAssignment,
    // Document validation
    validateDocumentApproval,
    // Comment validation
    validateUpdateComments,
    // Parameter validation
    validateObjectId
};
