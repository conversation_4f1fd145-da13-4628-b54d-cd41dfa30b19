/**
 * Tech Roadmap Controller
 * Handles the business logic for tech roadmap related operations
 */

const TechRoadmap = require('../../models/techRoadmap.model');
const User = require('../../models/user.model');
const Course = require('../../models/course.model');
const GeneralError = require('../../util/GeneralError');
const mongoose = require('mongoose');
const { ObjectId } = mongoose.Types;

/**
 * Get all tech roadmap assignments
 * @returns {Array} List of all tech roadmap assignments
 */
const getAllAssignments = async () => {
    try {
        const assignments = await TechRoadmap.find({})
            .populate('menteeId', 'firstName lastName email profilePic role')
            .populate('mentorId', 'firstName lastName email profilePic')
            .populate('courseId', 'name description duration')
            .sort({ createdAt: -1 });

        return assignments;
    } catch (error) {
        throw new GeneralError(`Error fetching assignments: ${error.message}`, 500);
    }
};

/**
 * Get assignments for a specific mentee
 * @param {string} menteeId - ID of the mentee
 * @returns {Array} List of assignments for the mentee
 */
const getMenteeAssignments = async (menteeId) => {
    try {
        if (!menteeId) {
            throw new GeneralError('Mentee ID is required', 400);
        }

        const assignments = await TechRoadmap.find({ menteeId })
            .populate('menteeId', 'firstName lastName email profilePic role')
            .populate('mentorId', 'firstName lastName email profilePic')
            .populate('courseId', 'name description duration')
            .sort({ createdAt: -1 });

        return assignments;
    } catch (error) {
        if (error instanceof GeneralError) {
            throw error;
        }
        throw new GeneralError(`Error fetching mentee assignments: ${error.message}`, 500);
    }
};

/**
 * Get assignments assigned by a specific mentor
 * @param {string} mentorId - ID of the mentor
 * @returns {Array} List of assignments assigned by the mentor
 */
const getMentorAssignments = async (mentorId) => {
    try {
        if (!mentorId) {
            throw new GeneralError('Mentor ID is required', 400);
        }

        const assignments = await TechRoadmap.find({ mentorId })
            .populate('menteeId', 'firstName lastName email profilePic role')
            .populate('mentorId', 'firstName lastName email profilePic')
            .populate('courseId', 'name description duration')
            .sort({ createdAt: -1 });

        return assignments;
    } catch (error) {
        if (error instanceof GeneralError) {
            throw error;
        }
        throw new GeneralError(`Error fetching mentor assignments: ${error.message}`, 500);
    }
};

/**
 * Create a new tech roadmap assignment
 * @param {Object} assignmentData - Data for the new assignment
 * @returns {Object} Created assignment
 */
const createAssignment = async (assignmentData) => {
    try {
        const { menteeId, mentorId, courseId, targetMonth, description, learningMedium } = assignmentData;

        if (!menteeId || !mentorId || !courseId) {
            throw new GeneralError('Mentee ID, Mentor ID, and Course ID are required', 400);
        }

        // Check if mentee exists
        const mentee = await User.findById(menteeId);
        if (!mentee) {
            throw new GeneralError('Mentee not found', 404);
        }

        // Check if mentor exists
        const mentor = await User.findById(mentorId);
        if (!mentor) {
            throw new GeneralError('Mentor not found', 404);
        }

        // Check if course exists
        const course = await Course.findById(courseId);
        if (!course) {
            throw new GeneralError('Course not found', 404);
        }

        // Create new assignment
        const newAssignment = new TechRoadmap({
            menteeId,
            mentorId,
            courseId,
            targetMonth: targetMonth || new Date(),
            description: description || course.description,
            learningMedium: learningMedium || '',
            completionStatus: 'pending',
            completionPercentage: 0,
            approvalStatus: 'pending'
        });

        await newAssignment.save();

        // Return the created assignment with populated fields
        const createdAssignment = await TechRoadmap.findById(newAssignment._id)
            .populate('menteeId', 'firstName lastName email profilePic role')
            .populate('mentorId', 'firstName lastName email profilePic')
            .populate('courseId', 'name description duration');

        return createdAssignment;
    } catch (error) {
        if (error instanceof GeneralError) {
            throw error;
        }
        throw new GeneralError(`Error creating assignment: ${error.message}`, 500);
    }
};

/**
 * Upload a document for an assignment
 * @param {string} assignmentId - ID of the assignment
 * @param {Object} fileData - File data including URL and original name
 * @returns {Object} Updated assignment with the new document
 */
const uploadDocument = async (assignmentId, fileData) => {
    try {
        if (!assignmentId || !fileData || !fileData.url) {
            throw new GeneralError('Assignment ID and file data are required', 400);
        }

        const assignment = await TechRoadmap.findById(assignmentId);
        if (!assignment) {
            throw new GeneralError('Assignment not found', 404);
        }

        // Initialize documents array if it doesn't exist
        if (!assignment.documents) {
            assignment.documents = [];
        }

        // Add the new document
        assignment.documents.push({
            url: fileData.url,
            originalName: fileData.originalName || 'document',
            uploadedAt: new Date(),
            approvalStatus: 'pending'
        });

        await assignment.save();

        // Return the updated assignment
        return await TechRoadmap.findById(assignmentId)
            .populate('menteeId', 'firstName lastName email profilePic role')
            .populate('mentorId', 'firstName lastName email profilePic')
            .populate('courseId', 'name description duration');
    } catch (error) {
        if (error instanceof GeneralError) {
            throw error;
        }
        throw new GeneralError(`Error uploading document: ${error.message}`, 500);
    }
};

/**
 * Update assignment completion status
 * @param {string} assignmentId - ID of the assignment
 * @param {string} status - New completion status
 * @returns {Object} Updated assignment
 */
const updateCompletionStatus = async (assignmentId, status) => {
    try {
        if (!assignmentId || !status) {
            throw new GeneralError('Assignment ID and status are required', 400);
        }

        if (!['pending', 'completed', 'in-progress'].includes(status)) {
            throw new GeneralError('Invalid status. Status must be pending, completed, or in-progress', 400);
        }

        const assignment = await TechRoadmap.findById(assignmentId);
        if (!assignment) {
            throw new GeneralError('Assignment not found', 404);
        }

        assignment.completionStatus = status;

        // If status is completed, set completion percentage to 100
        if (status === 'completed') {
            assignment.completionPercentage = 100;
        }

        await assignment.save();

        // Return the updated assignment
        return await TechRoadmap.findById(assignmentId)
            .populate('menteeId', 'firstName lastName email profilePic role')
            .populate('mentorId', 'firstName lastName email profilePic')
            .populate('courseId', 'name description duration');
    } catch (error) {
        if (error instanceof GeneralError) {
            throw error;
        }
        throw new GeneralError(`Error updating completion status: ${error.message}`, 500);
    }
};

module.exports = {
    getAllAssignments,
    getMenteeAssignments,
    getMentorAssignments,
    createAssignment,
    uploadDocument,
    updateCompletionStatus
};
