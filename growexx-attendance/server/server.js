/**
 * @name Server Configuration
 */
require("dd-trace").init({
  appsec: true,
});
const compression = require("compression");
const express = require("express");
const cookieParser = require("cookie-parser");
const app = express();
const swaggerRoutes = require("./services/swaggerRoutes");
const authRoutes = require("./routes/authRoutes");
const userRoutes = require("./routes/userRoutes");
const jiraRoutes = require("./routes/jiraRoutes");
const kraRoutes = require("./routes/kraRoutes");
const projectRoutes = require("./routes/projectRoutes");
const maintenanceRoutes = require("./routes/maintenanceRoutes");
const projectTrackerRoutes = require("./routes/projectTrackerRoutes");
const ragRoutes = require("./routes/ragRoutes");
const pliParametersRoutes = require("./routes/pliParameters.routes");
const pliRatingRoutes = require("./routes/pliRating.routes");
const projectSprintDataRoutes = require("./routes/projectSprintDataRoutes");
const pliRoutes = require("./routes/pliRoutes");
const techRoadmapRoutes = require("./routes/techRoadmap");

const cors = require("cors");
const methodOverride = require("method-override");
const i18n = require("i18n");
const morgan = require("morgan");
const helmet = require("helmet");

// Global Variables
global.DB_CONNECTION = require("mongoose");
global.CONSOLE_LOGGER = require("./util/logger");
global.CONSTANTS = require("./util/constants");
global.MESSAGES = require("./locales/en.json");
global.MOMENT = require("moment");
global._ = require("lodash");

const mongoose = require("mongoose");
mongoose.set("strictQuery", true);

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
  const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(
    process.env.DB_PASSWORD
  )}`;
  dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  maxPoolSize: 10,
};
const connectWithRetry = () => {
  CONSOLE_LOGGER.info("MongoDB connection with retry");
  DB_CONNECTION.connect(dbUrl, options)
    .then(() => {
      CONSOLE_LOGGER.info("MongoDB is connected");
    })
    .catch((err) => {
      CONSOLE_LOGGER.info(err);
      CONSOLE_LOGGER.info(
        "MongoDB connection unsuccessful, retry after 0.5 seconds."
      );
      setTimeout(connectWithRetry, 500);
    });
};
connectWithRetry();

if (process.env.LOCAL === "true") {
  app.use(express.static("../jsdocs/jsdocs"));
  app.use(
    "/auth/coverage",
    express.static(`${__dirname}/../coverage/lcov-report`)
  );
}

// Configure i18n for multilingual
i18n.configure({
  locales: ["en"],
  directory: `${__dirname}/locales`,
  extension: ".json",
  prefix: "",
  logDebugFn(msg) {
    if (process.env.LOCAL === "true") {
      CONSOLE_LOGGER.debug(`i18n::${CONSTANTS.LOG_LEVEL}`, msg);
    }
  },
});

app.use(compression());
app.use(helmet());
app.use(i18n.init);
app.use(cookieParser());

app.use(express.urlencoded({ limit: "50mb", extended: true }));
app.use(express.json({ limit: "50mb", extended: true }));

app.use(
  cors({
    origin: ["http://localhost:3000", "https://demo-timesheet.growexx.com"],
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    credentials: true,
    exposedHeaders: ["x-auth-token"],
  })
);

app.use(morgan("dev"));
app.use(methodOverride());
if (process.env.NODE_ENV !== "production") {
  app.use("/", swaggerRoutes);
}
// Landing Page
app.get("/", (req, res) => {
  res.send({
    status: "ok",
    date: MOMENT(),
  });
});

// cca maintenance
app.get("/cca-maintenance", (req, res) => {
  res.send({ maintenance: CONSTANTS.CHEATCODE_MAINTENANCE });
});

// Public endpoint for mentees grouped by mentor
app.get("/api/public/mentees-by-mentor", (req, res) => {
  // Call the controller method directly
  require("./services/mentee/menteeController").getMenteesGroupedByMentor(
    req,
    res
  );
});

app.use("/auth", authRoutes);
app.use("/user", userRoutes);
app.use("/jira", jiraRoutes);
app.use("/kra", kraRoutes);
app.use("/project", projectRoutes);
app.use("/maintenance", maintenanceRoutes);
app.use("/project-tracker", projectTrackerRoutes);
app.use("/rag", ragRoutes);
app.use("/api", pliParametersRoutes);
app.use("/api", pliRatingRoutes);
app.use("/project-sprint-data", projectSprintDataRoutes);
app.use("/pli", pliRoutes);
app.use("/tech-roadmap", techRoadmapRoutes);

app.use(express.static("public"));
module.exports = app;
