/**
 * Tech Roadmap Routes
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const courseService = require('../services/techRoadmap/courseService');
const assignCourseService = require('../services/techRoadmap/assignCourseService');
const documentApprovalService = require('../services/techRoadmap/documentApprovalService');
const commentService = require('../services/techRoadmap/commentService');
const auth = require('../middleware/auth');
const { checkRole } = require('../middleware/roleAuth');
const { ROLE } = require('../util/constants');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only pdf, jpg, jpeg and png files
    if (
      file.mimetype === 'application/pdf' ||
      file.mimetype === 'image/jpeg' ||
      file.mimetype === 'image/jpg' ||
      file.mimetype === 'image/png'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only PDF, JPG, JPEG, and PNG files are allowed'), false);
    }
  },
});

/**
 * @route POST /tech-roadmap/course
 * @desc Add a new course
 * @access Private - HR only
 */
router.post('/course', auth, checkRole([ROLE.HR, ROLE.ADMIN]), async (req, res) => {
  const result = await courseService.addCourse(req.body);
  return res.status(result.status).json(result);
});

/**
 * @route GET /tech-roadmap/course
 * @desc Get all courses
 * @access Private - All authenticated users
 */
router.get('/course', auth, async (req, res) => {
  const result = await courseService.getAllCourses();
  return res.status(result.status).json(result);
});

/**
 * @route POST /tech-roadmap/assign
 * @desc Assign a course to a mentee
 * @access Private
 */
router.post('/assign', auth, async (req, res) => {
  try {
    const result = await assignCourseService.assignCourse(req.body);
    return res.status(result.status).json(result);
  } catch (error) {
    return res.status(error.statusCode || 400).json({
      status: 0,
      message: error.message || 'Error assigning course'
    });
  }
});

/**
 * @route GET /tech-roadmap/mentee/:menteeId
 * @desc Get all courses assigned to a mentee
 * @access Private
 */
router.get('/mentee/:menteeId', auth, async (req, res) => {
  try {
    const result = await assignCourseService.getMenteeCourses(req.params.menteeId);
    return res.status(result.status).json(result);
  } catch (error) {
    return res.status(error.statusCode || 400).json({
      status: 0,
      message: error.message || 'Error retrieving courses'
    });
  }
});

/**
 * @route PATCH /tech-roadmap/status/:assignmentId
 * @desc Update course completion status
 * @access Private
 */
router.patch('/status/:assignmentId', auth, async (req, res) => {
  try {
    const result = await assignCourseService.updateCourseStatus(req.params.assignmentId, req.body);
    return res.status(result.status).json(result);
  } catch (error) {
    return res.status(error.statusCode || 400).json({
      status: 0,
      message: error.message || 'Error updating course status'
    });
  }
});

/**
 * @route GET /tech-roadmap/all-assignments
 * @desc Get all tech roadmap assignments
 * @access Private - All authenticated users
 */
router.get('/all-assignments', auth, async (req, res) => {
  try {
    const result = await assignCourseService.getAllAssignments();
    return res.status(result.status).json(result);
  } catch (error) {
    return res.status(error.statusCode || 400).json({
      status: 0,
      message: error.message || 'Error retrieving all assignments'
    });
  }
});

/**
 * @route POST /tech-roadmap/upload-document
 * @desc Upload a document for a course assignment
 * @access Public - Anyone can upload documents
 */
router.post('/upload-document', upload.single('document'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        status: 0,
        message: 'No file uploaded'
      });
    }

    const assignmentId = req.body.assignmentId;
    if (!assignmentId) {
      return res.status(400).json({
        status: 0,
        message: 'Assignment ID is required'
      });
    }

    const result = await assignCourseService.uploadDocument(assignmentId, req.file);
    return res.status(result.status).json(result);
  } catch (error) {
    return res.status(error.statusCode || 400).json({
      status: 0,
      message: error.message || 'Error uploading document'
    });
  }
});

/**
 * @route POST /tech-roadmap/document-approval
 * @desc Approve or reject a document
 * @access Private - Only authenticated users (mentors, HR, admin)
 */
router.post('/document-approval', auth, async (req, res) => {
  try {
    const { assignmentId, documentId, status, comment } = req.body;
    
    if (!assignmentId || !documentId || !status) {
      return res.status(400).json({
        status: 0,
        message: 'Assignment ID, Document ID, and status are required'
      });
    }
    
    // Get user ID from request if available, or use a default value
    const userId = req.user && req.user.id ? req.user.id : 'system';
    const result = await documentApprovalService.updateDocumentApprovalStatus(
      assignmentId,
      documentId,
      status,
      userId,
      comment
    );
    
    return res.status(200).json({
      status: 1,
      message: `Document ${status} successfully`,
      data: result
    });
  } catch (error) {
    return res.status(error.statusCode || 400).json({
      status: 0,
      message: error.message || 'Error updating document approval status'
    });
  }
});

/**
 * @route POST /tech-roadmap/update-comments
 * @desc Update mentor or mentee comments for a course assignment
 * @access Private - Only authenticated users
 */
router.post('/update-comments', auth, async (req, res) => {
  try {
    const { assignmentId, commentType, comment } = req.body;
    
    if (!assignmentId || !commentType || comment === undefined) {
      return res.status(400).json({
        status: 0,
        message: 'Assignment ID, comment type, and comment are required'
      });
    }
    
    const result = await commentService.updateComments(
      assignmentId,
      commentType,
      comment
    );
    
    return res.status(200).json({
      status: 1,
      message: `${commentType.charAt(0).toUpperCase() + commentType.slice(1)} comment updated successfully`,
      data: result
    });
  } catch (error) {
    return res.status(error.statusCode || 400).json({
      status: 0,
      message: error.message || 'Error updating comments'
    });
  }
});

router.delete("/assignment/:id", auth, async (req, res) => {
  try {
    const result = await assignCourseService.deleteCourseAssignment(
      req.params.id
    );
    res.status(200).json(result);
  } catch (error) {
    res.status(error.statusCode || 400).json({
      status: 0,
      message: error.message || "Error deleting course assignment",
    });
  }
});
 

// Serve uploaded files statically
router.use('/uploads', express.static('uploads'));

module.exports = router;
