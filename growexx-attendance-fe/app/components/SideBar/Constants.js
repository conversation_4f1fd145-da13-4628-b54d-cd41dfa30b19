/*
 * Sidebar Constants
 * This file contains constants used in Sidebar component.
 */

import React from 'react';
import {
  ApiOutlined,
  TeamOutlined,
  ProfileOutlined,
  ProjectOutlined,
  FundProjectionScreenOutlined,
  BookOutlined,
  BookFilled,
  // BarChartOutlined,
} from '@ant-design/icons';
import { ROUTES, ROLE_BASED_SIDEBAR_MENU, ROLES } from 'containers/constants';

export const MenuItems = [
  {
    to: ROUTES.CONNECTED_JIRA,
    tabName: 'Connected JIRA',
    icon: <ApiOutlined />,
  },
  {
    to: ROUTES.USERS,
    tabName: 'Employees',
    icon: <TeamOutlined />,
  },
  {
    to: ROUTES.PROJECTS,
    tabName: 'Projects',
    icon: <ProjectOutlined />,
  },
  {
    to: ROUTES.LOGS,
    tabName: 'Logs',
    icon: <ProfileOutlined />,
  },
  {
    to: ROUTES.PROJECT_TRACKER,
    tabName: 'Project Tracker',
    icon: <FundProjectionScreenOutlined />,
  },
  {
    to: ROUTES.RAG_REPORT,
    tabName: 'Rag Report',
    icon: <ProfileOutlined />,
  },
  // Tech Roadmap item removed as it's now accessible via Course Assignment page
  {
    to: ROUTES.COURSE_ASSIGNMENT,
    tabName: 'Course Assignment',
    icon: <BookFilled />,
  },
  /* {
    to: ROUTES.Mentee,
    tabName: 'PLI',
    icon: <BarChartOutlined />,
  }, */
];

/**
 * Filters Sidebar menu based on role
 * @param {string} role
 */
export const GET_FILTERED_MENU_ITEM = role =>
  MenuItems.filter(item =>
    // Check if route is private then role has access to it
    ROLE_BASED_SIDEBAR_MENU[role || ROLES.USER].includes(item.to),
  );
