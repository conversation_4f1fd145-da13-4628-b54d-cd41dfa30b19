import React, { useState, useEffect, useContext } from 'react';
import { Row, Col, Input, Modal, message } from 'antd';
import MenteeRoadmapForm from 'containers/MenteeTechRoadmap/MenteeRoadmapForm';
import request from 'utils/request';
import { API_ENDPOINTS } from 'containers/constants';
import { EmployeeProfileContext } from 'containers/EmployeeProfile/context';
import {
  DetailCard,
  Label,
  Value,
  DetailRow,
  HeaderCell,
  SaveButton,
  ContentWrapper,
} from './Styles/StyledPersonalPerformance';

const PersonalPerformance = () => {
  const [techRoadmapModalVisible, setTechRoadmapModalVisible] = useState(false);
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Get profile data from context if available
  const profileContext = useContext(EmployeeProfileContext);
  const profileData = profileContext ? profileContext.profile : null;

  // Fetch current user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const response = await request(API_ENDPOINTS.USER_DETAILS_API, {
          method: 'GET',
        });

        if (response && response.data) {
          console.log('User data fetched:', response.data);
          // Ensure the user data has a designation property
          const updatedUserData = {
            ...response.data,
            // Hardcode designation to 'Developers' for testing
            designation: 'Developers',
          };
          setUserData(updatedUserData);
          console.log('Updated user data with designation:', updatedUserData);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        message.error('Failed to fetch user data');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);
  const [performanceData, setPerformanceData] = useState({
    technologyRoadmap: {
      score: '85',
      comments: '',
      calculation: '90',
      weightageAverage: '45',
    },
    hrParameters: {
      score: '92',
      comments: '',
      calculation: '85',
      weightageAverage: '55',
    },
  });

  const handleCommentsChange = (parameter, value) => {
    setPerformanceData(prev => ({
      ...prev,
      [parameter]: {
        ...prev[parameter],
        comments: value,
      },
    }));
  };

  // Function to render the roadmap content based on available data
  const renderRoadmapContent = () => {
    // If profile data is available from context
    if (profileData) {
      return (
        <MenteeRoadmapForm
          menteeId={profileData.menteeId}
          mentorId={profileData.mentorId}
          menteeRole={profileData.designation}
          menteeName={profileData.name}
        />
      );
    }

    // Fallback to user data if profile context is not available
    if (userData) {
      return (
        <MenteeRoadmapForm
          menteeId={userData._id}
          mentorId={userData.reportingManagerId || ''}
          menteeRole={userData.designation}
          menteeName={userData.name}
        />
      );
    }

    // Show loading state if neither data is available
    return <div>Loading user data...</div>;
  };

  return (
    <ContentWrapper>
      <Row style={{ margin: 0, width: '100%' }}>
        {' '}
        <Col span={24}>
          {' '}
          <DetailCard title="Personal Performance">
            <DetailRow>
              {' '}
              <Col span={6}>
                {' '}
                <Label>Parameters</Label>{' '}
              </Col>{' '}
              <Col span={4}>
                {' '}
                <HeaderCell>Score</HeaderCell>{' '}
              </Col>{' '}
              <Col span={8}>
                {' '}
                <HeaderCell>Comments</HeaderCell>{' '}
              </Col>{' '}
              <Col span={3}>
                {' '}
                <HeaderCell>Calculation</HeaderCell>{' '}
              </Col>{' '}
              <Col span={3}>
                {' '}
                <HeaderCell>Weightage Average</HeaderCell>{' '}
              </Col>{' '}
            </DetailRow>

            {/* Technology Roadmap Row */}
            <DetailRow
              style={{ cursor: 'pointer' }}
              onClick={() => setTechRoadmapModalVisible(true)}
            >
              <Col span={6}>
                <Label style={{ color: '#6c5ce7' }}>Technology Roadmap</Label>
              </Col>
              <Col span={4}>
                <Value
                  style={{
                    height: '54px',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {performanceData.technologyRoadmap.score}
                </Value>
              </Col>
              <Col span={8}>
                <Input.TextArea
                  placeholder="Enter comments"
                  value={performanceData.technologyRoadmap.comments}
                  onChange={e =>
                    handleCommentsChange('technologyRoadmap', e.target.value)
                  }
                  rows={2}
                />
              </Col>
              <Col span={3}>
                <Value>{performanceData.technologyRoadmap.calculation}</Value>
              </Col>
              <Col span={3}>
                <Value>
                  {performanceData.technologyRoadmap.weightageAverage}
                </Value>
              </Col>
            </DetailRow>

            {/* HR Parameters Row */}
            <DetailRow>
              <Col span={6}>
                <Label>HR Parameters</Label>
              </Col>
              <Col span={4}>
                <Value
                  style={{
                    height: '54px',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {performanceData.hrParameters.score}
                </Value>
              </Col>
              <Col span={8}>
                <Input.TextArea
                  placeholder="Enter comments"
                  value={performanceData.hrParameters.comments}
                  onChange={e =>
                    handleCommentsChange('hrParameters', e.target.value)
                  }
                  rows={2}
                />
              </Col>
              <Col span={3}>
                <Value>{performanceData.hrParameters.calculation}</Value>
              </Col>
              <Col span={3}>
                <Value>{performanceData.hrParameters.weightageAverage}</Value>
              </Col>
            </DetailRow>

            <SaveButton type="primary">Save</SaveButton>

            {/* Tech Roadmap Modal */}
            <Modal
              title="Technology Roadmap"
              visible={techRoadmapModalVisible}
              onCancel={() => setTechRoadmapModalVisible(false)}
              footer={null}
              width={800}
            >
              {renderRoadmapContent()}
            </Modal>
          </DetailCard>
        </Col>
      </Row>
    </ContentWrapper>
  );
};

export default PersonalPerformance;
